# KRTR Mesh context configuration for context.app
name: KRTR Mesh
path: ~/krtr-mesh-local
apps: [VSCode, Terminal, Xcode, Arc]
urls:
  - https://github.com/Z0rlord/krtr-mesh
  - https://developer.apple.com/documentation/multipeerconnectivity
  - https://developer.apple.com/documentation/corebluetooth
  - https://noir-lang.org/
scripts:
  # Core development environment setup (parallel execution)
  - cd ~/krtr-mesh-local && swift build &
  - cd ~/krtr-mesh-local/landing-page && npm install && npm run dev &
  - cd ~/krtr-mesh-local && ./scripts/dev-shell.sh &

  # Quick build verification (run after setup)
  - cd ~/krtr-mesh-local && swift test

  # iOS development (on-demand)
  - cd ~/krtr-mesh-local && open KRTRMesh.xcodeproj
  - cd ~/krtr-mesh-local/krtr-native-ios && open KRTR.xcodeproj
  - cd ~/krtr-mesh-local/bitchat-base && open bitchat.xcodeproj

  # ZK circuit compilation (on-demand)
  - cd ~/krtr-mesh-local/circuits && nargo compile

  # Full project build (CI-style verification)
  - cd ~/krtr-mesh-local && swift build && cd krtr-native-ios && xcodebuild -project KRTR.xcodeproj -scheme KRTR build && cd ../bitchat-base && swift build
