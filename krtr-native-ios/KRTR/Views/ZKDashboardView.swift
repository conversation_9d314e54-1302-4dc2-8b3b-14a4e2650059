import SwiftUI

struct ZKDashboardView: View {
    @StateObject private var meshService: BluetoothMeshService
    @StateObject private var lightningService: LightningService
    @State private var showingSettings = false
    @State private var showingConnections = false
    @State private var refreshTimer = Timer.publish(every: 2, on: .main, in: .common).autoconnect()

    init(meshService: BluetoothMeshService) {
        self._meshService = StateObject(wrappedValue: meshService)

        // Initialize Lightning service with test configuration
        // TODO: Replace with actual Lightning node configuration
        self._lightningService = StateObject(wrappedValue: LightningServiceFactory.createTestService())
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Network Status Header
                    NetworkStatusHeaderView(meshService: meshService)

                    // Quick Stats Grid
                    QuickStatsGridView(meshService: meshService)

                    // Active Connections
                    ActiveConnectionsView(meshService: meshService)

                    // Recent Activity
                    RecentActivityView(meshService: meshService)

                    // Lightning Dashboard Section
                    LightningDashboardSection(lightningService: lightningService)

                    // Quick Actions
                    DashboardQuickActionsView(
                        showingSettings: $showingSettings,
                        showingConnections: $showingConnections,
                        meshService: meshService
                    )

                    Spacer(minLength: 50)
                }
                .padding()
            }
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: { showingSettings = true }) {
                        Image(systemName: "gear")
                    }
                }
            }
            .sheet(isPresented: $showingSettings) {
                DashboardSettingsView()
            }
            .sheet(isPresented: $showingConnections) {
                ConnectionsDetailView(meshService: meshService)
            }
            .onReceive(refreshTimer) { _ in
                // Refresh dashboard data
            }
            .onAppear {
                // Connect Lightning service when view appears
                Task {
                    do {
                        try await lightningService.connect()
                    } catch {
                        print("⚠️ Failed to connect to Lightning node: \(error)")
                    }
                }
            }
        }
    }
}

struct NetworkStatusHeaderView: View {
    let meshService: BluetoothMeshService

    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Network Status")
                        .font(.title2)
                        .fontWeight(.bold)

                    HStack {
                        Circle()
                            .fill(meshService.isConnected ? .green : .red)
                            .frame(width: 8, height: 8)
                        Text(meshService.isConnected ? "Connected" : "Disconnected")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Image(systemName: "network")
                        .font(.system(size: 32))
                        .foregroundColor(.blue)

                    Text("\(meshService.connectedPeers.count) peers")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Signal Strength Indicator
            HStack {
                Text("Signal Strength")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Spacer()

                HStack(spacing: 2) {
                    ForEach(0..<5) { index in
                        Rectangle()
                            .fill(index < getSignalBars() ? .green : .gray.opacity(0.3))
                            .frame(width: 4, height: CGFloat(8 + index * 2))
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }

    private func getSignalBars() -> Int {
        // Simulate signal strength based on connected peers
        min(meshService.connectedPeers.count, 5)
    }
}

struct QuickStatsGridView: View {
    let meshService: BluetoothMeshService

    var body: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            DashboardStatCard(
                title: "Connected Peers",
                value: "\(meshService.connectedPeers.count)",
                icon: "person.3.fill",
                color: .blue
            )

            DashboardStatCard(
                title: "Messages Sent",
                value: "\(getMessagesSent())",
                icon: "paperplane.fill",
                color: .green
            )

            DashboardStatCard(
                title: "Data Transferred",
                value: formatDataSize(getTotalDataTransferred()),
                icon: "arrow.up.arrow.down",
                color: .orange
            )

            DashboardStatCard(
                title: "Uptime",
                value: formatUptime(getUptime()),
                icon: "clock.fill",
                color: .purple
            )
        }
    }

    private func getMessagesSent() -> Int {
        // Get actual message count from mesh service
        return 42 // Placeholder
    }

    private func getTotalDataTransferred() -> Int {
        // Get actual data transfer stats
        return 1024 * 1024 * 2 // 2MB placeholder
    }

    private func getUptime() -> TimeInterval {
        // Get actual uptime
        return 3600 * 2 // 2 hours placeholder
    }

    private func formatDataSize(_ bytes: Int) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: Int64(bytes))
    }

    private func formatUptime(_ seconds: TimeInterval) -> String {
        let hours = Int(seconds) / 3600
        let minutes = (Int(seconds) % 3600) / 60
        return "\(hours)h \(minutes)m"
    }
}

struct DashboardStatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            Text(value)
                .font(.headline)
                .fontWeight(.bold)

            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct ActiveConnectionsView: View {
    let meshService: BluetoothMeshService

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Active Connections")
                    .font(.headline)
                    .fontWeight(.bold)

                Spacer()

                Button("View All") {
                    // Show all connections
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            if meshService.connectedPeers.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "wifi.slash")
                        .font(.title2)
                        .foregroundColor(.gray)
                    Text("No active connections")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(Array(meshService.connectedPeers.prefix(3)), id: \.self) { peer in
                        ConnectionRowView(peer: peer)
                    }

                    if meshService.connectedPeers.count > 3 {
                        Text("+ \(meshService.connectedPeers.count - 3) more")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 4)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 2)
    }
}

struct ConnectionRowView: View {
    let peer: String

    var body: some View {
        HStack {
            Circle()
                .fill(.green)
                .frame(width: 8, height: 8)

            VStack(alignment: .leading, spacing: 2) {
                Text(peer)
                    .font(.subheadline)
                    .fontWeight(.medium)
                Text("Connected")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            VStack(alignment: .trailing, spacing: 2) {
                Text("Strong")
                    .font(.caption)
                    .foregroundColor(.green)
                Image(systemName: "wifi")
                    .font(.caption)
                    .foregroundColor(.green)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct RecentActivityView: View {
    let meshService: BluetoothMeshService

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Activity")
                .font(.headline)
                .fontWeight(.bold)

            LazyVStack(spacing: 8) {
                ActivityRowView(
                    icon: "paperplane.fill",
                    title: "Message sent to mesh",
                    time: "2 min ago",
                    color: .blue
                )

                ActivityRowView(
                    icon: "person.badge.plus",
                    title: "New peer connected",
                    time: "5 min ago",
                    color: .green
                )

                ActivityRowView(
                    icon: "arrow.down.circle",
                    title: "Data received",
                    time: "8 min ago",
                    color: .orange
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 2)
    }
}

struct ActivityRowView: View {
    let icon: String
    let title: String
    let time: String
    let color: Color

    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(color)
                .frame(width: 20)

            Text(title)
                .font(.subheadline)

            Spacer()

            Text(time)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

struct DashboardQuickActionsView: View {
    @Binding var showingSettings: Bool
    @Binding var showingConnections: Bool
    let meshService: BluetoothMeshService

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.bold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                DashboardActionButton(
                    title: "Send Message",
                    subtitle: "Broadcast to mesh",
                    icon: "paperplane.fill",
                    color: .blue
                ) {
                    // Send message action
                }

                DashboardActionButton(
                    title: "Find Peers",
                    subtitle: "Discover nearby",
                    icon: "magnifyingglass",
                    color: .green
                ) {
                    // Find peers action
                }

                DashboardActionButton(
                    title: "Network Info",
                    subtitle: "View details",
                    icon: "info.circle",
                    color: .orange
                ) {
                    showingConnections = true
                }

                DashboardActionButton(
                    title: "Settings",
                    subtitle: "Configure app",
                    icon: "gear",
                    color: .purple
                ) {
                    showingSettings = true
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 2)
    }
}

struct DashboardActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)

                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(subtitle)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// Placeholder views for sheets
struct DashboardSettingsView: View {
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Dashboard Settings")
                    .font(.title)
                Spacer()
            }
            .padding()
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct ConnectionsDetailView: View {
    let meshService: BluetoothMeshService
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack {
                Text("Network Connections")
                    .font(.title)
                Spacer()
            }
            .padding()
            .navigationTitle("Connections")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") { dismiss() }
                }
            }
        }
    }
}

struct ZKQuickActionsView: View {
    @Binding var showingTestView: Bool
    @Binding var showingGroupJoin: Bool
    @Binding var showingReputationProof: Bool
    @Binding var showingMessageAuth: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .fontWeight(.bold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ZKActionButton(
                    title: "Test ZK",
                    subtitle: "Run tests",
                    icon: "testtube.2",
                    color: .blue
                ) {
                    showingTestView = true
                }
                
                ZKActionButton(
                    title: "Join Group",
                    subtitle: "Anonymous join",
                    icon: "person.3",
                    color: .green
                ) {
                    showingGroupJoin = true
                }
                
                ZKActionButton(
                    title: "Prove Reputation",
                    subtitle: "Share proof",
                    icon: "star.circle",
                    color: .orange
                ) {
                    showingReputationProof = true
                }
                
                ZKActionButton(
                    title: "Auth Message",
                    subtitle: "Anonymous auth",
                    icon: "message.badge.circle",
                    color: .purple
                ) {
                    showingMessageAuth = true
                }
            }
        }
    }
}

struct ZKActionButton: View {
    let title: String
    let subtitle: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(subtitle)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct ZKStatisticsView: View {
    let zkService: MockZKService
    let zkMeshProtocol: ZKMeshProtocol
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Statistics")
                .font(.headline)
                .fontWeight(.bold)
            
            VStack(spacing: 12) {
                let zkStats = zkService.getStats()
                let (_, meshStats) = zkMeshProtocol.getZKMeshStats()

                ZKStatRow(label: "Total Proofs", value: "\(zkStats.totalProofs)")
                ZKStatRow(label: "Average Duration", value: String(format: "%.3fs", zkStats.averageDuration))
                ZKStatRow(label: "Mesh Messages Sent", value: "\(meshStats.totalZKMessagesSent)")
                ZKStatRow(label: "Mesh Messages Received", value: "\(meshStats.totalZKMessagesReceived)")
                ZKStatRow(label: "ZK Proofs Generated", value: "\(meshStats.totalZKProofsGenerated)")
                ZKStatRow(label: "ZK Proofs Verified", value: "\(meshStats.totalZKProofsVerified)")
                ZKStatRow(label: "Success Rate", value: String(format: "%.1f%%", zkStats.successRate * 100))
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }
}

struct ZKStatRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

// MARK: - Missing View Components

struct ZKTestView: View {
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("ZK Test View")
                    .font(.title)
                    .fontWeight(.bold)

                Text("Zero-Knowledge proof testing interface")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("Coming Soon...")
                    .font(.headline)
                    .foregroundColor(.gray)

                Spacer()
            }
            .padding()
            .navigationTitle("ZK Testing")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct ZKGroupJoinView: View {
    let zkMeshProtocol: ZKMeshProtocol

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Group Join")
                    .font(.title)
                    .fontWeight(.bold)

                Text("Join groups with zero-knowledge membership proofs")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("Coming Soon...")
                    .font(.headline)
                    .foregroundColor(.gray)

                Spacer()
            }
            .padding()
            .navigationTitle("Group Join")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct ZKReputationProofView: View {
    let zkMeshProtocol: ZKMeshProtocol

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Reputation Proof")
                    .font(.title)
                    .fontWeight(.bold)

                Text("Prove reputation without revealing exact score")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("Coming Soon...")
                    .font(.headline)
                    .foregroundColor(.gray)

                Spacer()
            }
            .padding()
            .navigationTitle("Reputation Proof")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

struct ZKMessageAuthView: View {
    let zkMeshProtocol: ZKMeshProtocol

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Message Authentication")
                    .font(.title)
                    .fontWeight(.bold)

                Text("Authenticate messages with zero-knowledge proofs")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                Spacer()

                Text("Coming Soon...")
                    .font(.headline)
                    .foregroundColor(.gray)

                Spacer()
            }
            .padding()
            .navigationTitle("Message Auth")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

#Preview {
    ZKDashboardView(meshService: BluetoothMeshService())
}
