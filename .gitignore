# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots
*/fastlane/test_output

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/
web-build/
dist/

# Native project folders (for Prebuild/CNG workflow)
/android
/ios

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# KRTR specific
*.log
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Crypto keys (never commit)
*.key
*.pem
identity_keys/

# Cache directories
.cache/
tmp/
temp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# React Native
.react-native/

# Activity logs and documentation with sensitive information
docs/activity.md
docs/activity-*.md
*.activity.md
**/activity.log

# Swift Package Manager build artifacts and dependencies
.build/
.swiftpm/
Package.resolved

# Xcode project files (generated)
*.xcodeproj/
*.xcworkspace/

# Embedded git repositories and external projects
bitchat-base/
krtr-mesh-swift/

# Augment AI artifacts
.augment/
